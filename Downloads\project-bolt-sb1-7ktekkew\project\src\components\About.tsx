import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

const About = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="about" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-stretch"
        >
          {/* Content Column */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col justify-center space-y-8 lg:pr-8"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-section-mobile md:text-section font-heading font-bold text-primary"
                variants={itemVariants}
              >
                Shreyas Infra Projects Pvt. Ltd.
              </motion.h2>

              <motion.div
                className="w-20 h-1 bg-secondary"
                variants={itemVariants}
              />

              <motion.p
                className="text-body-lg md:text-body-lg text-charcoal font-body"
                variants={itemVariants}
              >
                Established in 2010, Shreyas Infra Projects Pvt. Ltd. has
                emerged as a leading developer specializing in premium plotted
                developments across North Bengaluru. Our expertise lies in
                identifying high-growth corridors and creating thoughtfully
                planned communities that offer exceptional investment potential
                and quality living experiences.
              </motion.p>

              <motion.p
                className="text-body-lg md:text-body-lg text-charcoal font-body"
                variants={itemVariants}
              >
                Our flagship Devanahalli project represents our commitment to
                excellence - a 30-acre premium plotted development strategically
                located in North Bengaluru's fastest-growing investment hotspot.
                With proximity to major employment hubs like Foxconn, SAP Labs,
                and Infosys, combined with world-class amenities and
                infrastructure, we create communities that appreciate in value
                while providing an exceptional lifestyle.
              </motion.p>

              <motion.div
                className="grid grid-cols-2 gap-6 mt-8"
                variants={itemVariants}
              >
                <div className="bg-primary/5 rounded-lg p-4">
                  <h4 className="font-heading font-semibold text-primary mb-2">
                    Established
                  </h4>
                  <p className="text-body text-charcoal">2010</p>
                </div>
                <div className="bg-primary/5 rounded-lg p-4">
                  <h4 className="font-heading font-semibold text-primary mb-2">
                    Specialization
                  </h4>
                  <p className="text-body text-charcoal">
                    Plotted Developments
                  </p>
                </div>
                <div className="bg-primary/5 rounded-lg p-4">
                  <h4 className="font-heading font-semibold text-primary mb-2">
                    Focus Area
                  </h4>
                  <p className="text-body text-charcoal">North Bengaluru</p>
                </div>
                <div className="bg-primary/5 rounded-lg p-4">
                  <h4 className="font-heading font-semibold text-primary mb-2">
                    Current Project
                  </h4>
                  <p className="text-body text-charcoal">
                    Devanahalli - 30 Acres
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Image Column */}
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-center lg:pl-8"
          >
            <motion.div
              className="relative overflow-hidden rounded-2xl shadow-2xl w-full h-[300px] lg:h-[400px]"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.4 }}
            >
              <img
                src="https://images.pexels.com/photos/1571463/pexels-photo-1571463.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Luxury property interior showcasing modern design"
                className="w-full h-[300px] lg:h-[400px] object-cover"
                loading="lazy"
              />

              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
