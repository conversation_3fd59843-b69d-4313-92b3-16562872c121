import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Waves,
  Utensils,
  Dumbbell,
  Car,
  Wifi,
  Shield,
  Zap,
  TreePine,
  Wine,
  Camera,
  Headphones,
  Thermometer,
} from "lucide-react";

const Amenities = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const amenities = [
    {
      icon: Waves,
      title: "Infinity Pool",
      description: "Spectacular infinity pool with ocean views",
      color: "bg-blue-500/20 text-blue-400",
    },
    {
      icon: Utensils,
      title: "Gourmet Kitchen",
      description: "Professional-grade appliances and custom cabinetry",
      color: "bg-orange-500/20 text-orange-400",
    },
    {
      icon: Dumbbell,
      title: "Private Gym",
      description: "Fully equipped fitness center with ocean views",
      color: "bg-red-500/20 text-red-400",
    },
    {
      icon: Car,
      title: "Parking",
      description: "3-car garage with electric vehicle charging",
      color: "bg-gray-500/20 text-gray-400",
    },
    {
      icon: Wifi,
      title: "Smart Home",
      description: "Integrated automation and high-speed internet",
      color: "bg-green-500/20 text-green-400",
    },
    {
      icon: Shield,
      title: "Security",
      description: "24/7 monitoring and concierge services",
      color: "bg-purple-500/20 text-purple-400",
    },
    {
      icon: Zap,
      title: "Solar Power",
      description: "Sustainable energy with battery backup",
      color: "bg-yellow-500/20 text-yellow-400",
    },
    {
      icon: TreePine,
      title: "Garden",
      description: "Private landscaped gardens and outdoor living",
      color: "bg-emerald-500/20 text-emerald-400",
    },
    {
      icon: Wine,
      title: "Wine Cellar",
      description: "Temperature-controlled wine storage",
      color: "bg-pink-500/20 text-pink-400",
    },
    {
      icon: Camera,
      title: "Media Room",
      description: "Home theater with premium sound system",
      color: "bg-indigo-500/20 text-indigo-400",
    },
    {
      icon: Headphones,
      title: "Music Studio",
      description: "Soundproofed recording and practice space",
      color: "bg-teal-500/20 text-teal-400",
    },
    {
      icon: Thermometer,
      title: "Spa & Wellness",
      description: "Private spa with sauna and massage room",
      color: "bg-cyan-500/20 text-cyan-400",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const hexagonVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="amenities" className="py-8 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-playfair font-bold text-primary">
              Luxury Amenities
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-charcoal font-montserrat max-w-2xl mx-auto">
              Every detail has been carefully considered to provide an
              unparalleled lifestyle experience with world-class amenities and
              services.
            </p>
          </motion.div>

          {/* Amenities Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {amenities.map((amenity, index) => (
              <motion.div
                key={index}
                variants={hexagonVariants}
                className="group relative"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Hexagon Shape */}
                <div className="relative">
                  <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:bg-primary border-2 border-transparent group-hover:border-secondary">
                    {/* Icon */}
                    <div
                      className={`w-16 h-16 rounded-full ${amenity.color} flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}
                    >
                      <amenity.icon className="h-8 w-8" />
                    </div>

                    {/* Content */}
                    <div className="text-center space-y-2">
                      <h3 className="text-lg font-playfair font-semibold text-primary group-hover:text-accent transition-colors duration-300">
                        {amenity.title}
                      </h3>
                      <p className="text-sm text-charcoal group-hover:text-accent/90 font-montserrat transition-colors duration-300">
                        {amenity.description}
                      </p>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                    <div className="w-full h-full rounded-2xl bg-gradient-to-r from-secondary/20 to-primary/20 blur-xl transform scale-110"></div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Featured Amenity Section */}
          <motion.div
            variants={itemVariants}
            className="bg-primary rounded-2xl p-8 md:p-12 text-center space-y-8"
          >
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-playfair font-bold text-accent">
                Exclusive Concierge Services
              </h3>
              <p className="text-body-mobile md:text-body text-accent/90 font-montserrat max-w-3xl mx-auto">
                Experience personalized luxury with our dedicated concierge
                team, available 24/7 to cater to your every need. From private
                chef services to yacht charters, we ensure your lifestyle is
                seamlessly managed.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                className="space-y-3"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-12 h-12 bg-secondary rounded-full flex items-center justify-center mx-auto">
                  <Utensils className="h-6 w-6 text-primary" />
                </div>
                <h4 className="text-accent font-montserrat font-semibold">
                  Private Chef
                </h4>
                <p className="text-accent/70 text-sm font-montserrat">
                  Michelin-starred dining experiences in your home
                </p>
              </motion.div>

              <motion.div
                className="space-y-3"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-12 h-12 bg-secondary rounded-full flex items-center justify-center mx-auto">
                  <Car className="h-6 w-6 text-primary" />
                </div>
                <h4 className="text-accent font-montserrat font-semibold">
                  Transportation
                </h4>
                <p className="text-accent/70 text-sm font-montserrat">
                  Private driver and luxury vehicle services
                </p>
              </motion.div>

              <motion.div
                className="space-y-3"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-12 h-12 bg-secondary rounded-full flex items-center justify-center mx-auto">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h4 className="text-accent font-montserrat font-semibold">
                  Security
                </h4>
                <p className="text-accent/70 text-sm font-montserrat">
                  Professional security and maintenance services
                </p>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Amenities;
