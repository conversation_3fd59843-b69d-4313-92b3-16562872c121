import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  MapPin,
  Navigation,
  Clock,
  Car,
  Plane,
  ShoppingBag,
  Utensils,
  GraduationCap,
  Building,
  Trees,
  Waves,
  Mountain,
} from "lucide-react";
import GoogleMap from "./GoogleMap";

const Location = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeTab, setActiveTab] = useState("amenities");

  const nearbyAmenities = [
    {
      icon: Plane,
      name: "Kempegowda International Airport",
      distance: "20 minutes",
      type: "Airport",
      description: "Major international airport connectivity",
      color: "bg-blue-500/20 text-blue-400",
    },
    {
      icon: Car,
      name: "NH-44 Highway",
      distance: "5 minutes",
      type: "Highway",
      description: "Major highway connectivity to Bengaluru",
      color: "bg-green-500/20 text-green-400",
    },
    {
      icon: GraduationCap,
      name: "Harrow International School",
      distance: "10 minutes",
      type: "Education",
      description: "Premium international school",
      color: "bg-purple-500/20 text-purple-400",
    },
    {
      icon: Building,
      name: "Foxconn iPhone Campus",
      distance: "15 minutes",
      type: "Employment",
      description: "₹22,000 Crore manufacturing facility",
      color: "bg-indigo-500/20 text-indigo-400",
    },
    {
      icon: Mountain,
      name: "Nandi Hills",
      distance: "15 minutes",
      type: "Recreation",
      description: "Popular hill station and tourist spot",
      color: "bg-green-500/20 text-green-400",
    },
    {
      icon: GraduationCap,
      name: "Amity University",
      distance: "12 minutes",
      type: "Education",
      description: "Leading private university",
      color: "bg-orange-500/20 text-orange-400",
    },
    {
      icon: Building,
      name: "SAP Labs Campus",
      distance: "18 minutes",
      type: "Employment",
      description: "Major IT development center",
      color: "bg-blue-500/20 text-blue-400",
    },
    {
      icon: GraduationCap,
      name: "GITAM University",
      distance: "15 minutes",
      type: "Education",
      description: "Renowned educational institution",
      color: "bg-pink-500/20 text-pink-400",
    },
  ];

  const transportation = [
    {
      icon: Car,
      name: "Hebbal",
      time: "25 minutes",
      method: "Drive",
    },
    {
      icon: Plane,
      name: "Kempegowda Airport",
      time: "20 minutes",
      method: "Drive",
    },
    {
      icon: Car,
      name: "Yelahanka",
      time: "20 minutes",
      method: "Drive",
    },
    {
      icon: Navigation,
      name: "Metro Phase 2 (Upcoming)",
      time: "8 minutes",
      method: "Metro",
    },
    {
      icon: Car,
      name: "Bengaluru City Center",
      time: "45 minutes",
      method: "Drive",
    },
  ];

  const neighborhoods = [
    {
      name: "Devanahalli",
      image:
        "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "North Bengaluru's fastest-growing investment hotspot with excellent connectivity",
      highlights: ["Airport Proximity", "Industrial Growth", "Educational Hub"],
    },
    {
      name: "Hebbal",
      image:
        "https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "Established locality with excellent infrastructure and connectivity",
      highlights: ["IT Corridor", "Shopping Centers", "Healthcare Facilities"],
    },
    {
      name: "Yelahanka",
      image:
        "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800",
      description:
        "Well-developed area with good social infrastructure and amenities",
      highlights: ["Educational Institutions", "Parks", "Commercial Centers"],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="location" className="py-2 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-6"
        >
          {/* <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-playfair font-bold text-primary">
              Strategic Devanahalli Location
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-charcoal font-montserrat max-w-2xl mx-auto">
              Located in North Bengaluru's fastest-growing investment corridor,
              our Devanahalli plotted development offers exceptional
              connectivity to the airport, major employment hubs, and premium
              educational institutions.
            </p>
          </motion.div> */}
          {/* Google Maps Section */}
          <motion.div variants={itemVariants}>
            <GoogleMap />
          </motion.div>
          {/* Tabs Navigation */}
          <motion.div variants={itemVariants} className="flex justify-center">
            <div className="flex space-x-1 bg-white rounded-full p-2 shadow-lg">
              {[
                { id: "amenities", label: "Nearby Amenities" },
                { id: "transport", label: "Transportation" },
                { id: "neighborhoods", label: "Neighborhoods" },
              ].map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 rounded-full font-montserrat font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? "bg-primary text-accent shadow-lg"
                      : "text-charcoal hover:text-primary"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tab.label}
                </motion.button>
              ))}
            </div>
          </motion.div>
          {/* Tab Content */}
          <motion.div variants={itemVariants}>
            {activeTab === "amenities" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {nearbyAmenities.map((amenity, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
                    whileHover={{ y: -5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-start space-x-4">
                      <div
                        className={`w-12 h-12 rounded-full ${amenity.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                      >
                        <amenity.icon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-playfair font-semibold text-primary">
                            {amenity.name}
                          </h4>
                          <span className="text-secondary font-montserrat text-sm font-medium">
                            {amenity.distance}
                          </span>
                        </div>
                        <p className="text-xs text-secondary font-montserrat font-medium mb-1">
                          {amenity.type}
                        </p>
                        <p className="text-charcoal/70 font-montserrat text-sm">
                          {amenity.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {activeTab === "transport" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {transportation.map((transport, index) => (
                    <motion.div
                      key={index}
                      className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ y: -5, scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <transport.icon className="h-8 w-8 text-primary" />
                      </div>
                      <h4 className="font-playfair font-semibold text-primary mb-2">
                        {transport.name}
                      </h4>
                      <div className="flex items-center justify-center space-x-2 text-secondary">
                        <Clock className="h-4 w-4" />
                        <span className="font-montserrat text-sm">
                          {transport.time}
                        </span>
                      </div>
                      <p className="text-charcoal/70 font-montserrat text-xs mt-1">
                        by {transport.method}
                      </p>
                    </motion.div>
                  ))}
                </div>

                <div className="bg-primary rounded-2xl p-8 text-center">
                  <h3 className="text-2xl font-playfair font-bold text-accent mb-4">
                    Convenient Transportation Hub
                  </h3>
                  <p className="text-accent/90 font-montserrat max-w-2xl mx-auto">
                    Located at the crossroads of Los Angeles' most desirable
                    destinations, our properties offer unmatched accessibility
                    to business districts, entertainment venues, and cultural
                    landmarks.
                  </p>
                </div>
              </motion.div>
            )}

            {activeTab === "neighborhoods" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                {neighborhoods.map((neighborhood, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-2xl overflow-hidden shadow-luxury group"
                    whileHover={{ y: -8 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={neighborhood.image}
                        alt={neighborhood.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-primary/50 to-transparent" />
                      <div className="absolute bottom-4 left-4">
                        <h3 className="text-xl font-playfair font-bold text-accent">
                          {neighborhood.name}
                        </h3>
                      </div>
                    </div>

                    <div className="p-6 space-y-4">
                      <p className="text-charcoal font-montserrat leading-relaxed">
                        {neighborhood.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="font-montserrat font-semibold text-primary text-sm">
                          Key Highlights:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {neighborhood.highlights.map((highlight, idx) => (
                            <span
                              key={idx}
                              className="px-3 py-1 bg-accent rounded-full text-xs font-montserrat text-charcoal"
                            >
                              {highlight}
                            </span>
                          ))}
                        </div>
                      </div>

                      <motion.button
                        className="w-full bg-primary hover:bg-primary/90 text-accent py-3 rounded-lg font-montserrat font-medium transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Explore Area
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Location;
