import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { properties } from "../data/properties";

const Properties = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
  };

  return (
    <section
      id="properties"
      className="min-h-screen flex items-center section-luxury py-8 lg:py-12 xl:py-16"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div
            variants={itemVariants}
            className="text-center space-y-6 mb-8"
          >
            <h2 className="text-3xl md:text-5xl font-heading font-bold text-luxury">
              Devanahalli Premium Plotted Development
            </h2>
            <div className="divider-premium w-24 mx-auto" />
            <p className="text-lg md:text-xl text-charcoal-600 font-body max-w-4xl mx-auto leading-relaxed">
              Experience North Bengaluru's most promising investment opportunity
              - a 30-acre premium plotted development strategically located near
              major employment hubs.
            </p>
          </motion.div>

          <motion.div variants={itemVariants}>
            {properties.map((property) => (
              <div
                key={property.id}
                className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start card-luxury rounded-3xl p-6 lg:p-8 relative overflow-hidden"
              >
                {/* Images Section */}
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="flex flex-col space-y-5 h-full"
                >
                  {/* Main Image */}
                  <div className="relative overflow-hidden rounded-3xl shadow-luxury-lg flex-1 group">
                    <img
                      src={property.images[0]}
                      alt={property.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700 min-h-[280px]"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-primary-900/20 via-transparent to-transparent" />
                    <div className="absolute top-4 left-4 glass-effect px-4 py-2 rounded-full">
                      <span className="text-sm font-semibold text-primary-800 bg-gradient-to-r from-secondary-600 to-secondary-500 bg-clip-text text-transparent">
                        {property.status === "pre-launch"
                          ? "Pre-Launch"
                          : property.status}
                      </span>
                    </div>
                    <div className="absolute bottom-4 right-4 glass-effect px-3 py-1.5 rounded-lg">
                      <span className="text-xs font-medium text-primary-700">
                        Premium Location
                      </span>
                    </div>
                  </div>

                  {/* Gallery Images */}
                  <div className="grid grid-cols-3 gap-4">
                    {property.images.slice(1, 4).map((image, imgIndex) => (
                      <div
                        key={imgIndex}
                        className="relative overflow-hidden rounded-xl shadow-card group"
                      >
                        <img
                          src={image}
                          alt={`${property.title} ${imgIndex + 2}`}
                          className="w-full h-20 object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-primary-900/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Content Section */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex flex-col space-y-6 h-full justify-between"
                >
                  <div className="space-y-4">
                    <h3 className="text-3xl md:text-4xl font-heading font-bold text-luxury leading-tight">
                      {property.title}
                    </h3>
                    <div className="divider-luxury w-16" />
                    <p className="text-charcoal-600 font-body text-lg leading-relaxed">
                      {property.description}
                    </p>
                  </div>

                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 glass-effect rounded-xl border border-secondary-200/30 group hover:shadow-glow transition-all duration-300">
                      <div className="text-2xl font-heading font-bold text-price mb-2">
                        ₹{(property.price / 10000000).toFixed(1)} Cr
                      </div>
                      <div className="text-sm text-charcoal-500 font-medium">
                        Starting Price
                      </div>
                    </div>
                    <div className="text-center p-4 glass-effect rounded-xl border border-primary-200/30 group hover:shadow-card-hover transition-all duration-300">
                      <div className="text-2xl font-heading font-bold text-primary-700 mb-2">
                        {property.totalAcres}
                      </div>
                      <div className="text-sm text-charcoal-500 font-medium">
                        Total Acres
                      </div>
                    </div>
                    <div className="text-center p-4 glass-effect rounded-xl border border-primary-200/30 group hover:shadow-card-hover transition-all duration-300">
                      <div className="text-2xl font-heading font-bold text-primary-700 mb-2">
                        {property.phases?.length || 0}
                      </div>
                      <div className="text-sm text-charcoal-500 font-medium">
                        Phases
                      </div>
                    </div>
                    <div className="text-center p-4 glass-effect rounded-xl border border-secondary-200/30 group hover:shadow-glow transition-all duration-300">
                      <div className="text-2xl font-heading font-bold text-secondary-600 mb-2">
                        20 min
                      </div>
                      <div className="text-sm text-charcoal-500 font-medium">
                        To Airport
                      </div>
                    </div>
                  </div>

                  {/* Key Features */}
                  <div className="flex-1 space-y-4">
                    <h4 className="text-xl font-heading font-semibold text-primary-800 mb-4">
                      Key Features
                    </h4>
                    <div className="grid grid-cols-1 gap-3">
                      {property.features
                        .slice(0, 6)
                        .map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-50/50 transition-colors duration-200"
                          >
                            <div className="w-2 h-2 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex-shrink-0 shadow-sm"></div>
                            <span className="text-charcoal-600 font-body text-sm font-medium">
                              {feature}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 mt-auto pt-4">
                    <motion.button
                      className="flex-1 btn-luxury px-6 py-3.5 rounded-xl font-accent font-semibold text-sm tracking-wide"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Schedule Site Visit
                    </motion.button>
                    <motion.button
                      className="flex-1 btn-outline-luxury px-6 py-3.5 rounded-xl font-accent font-semibold text-sm tracking-wide"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Download Brochure
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Properties;
