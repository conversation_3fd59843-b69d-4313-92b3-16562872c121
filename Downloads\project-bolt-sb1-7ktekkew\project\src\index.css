@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography Base Styles */
@layer base {
  html {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0,
      "onum" 1, "lnum" 0, "dlig" 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply font-body text-body text-charcoal;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  /* Heading defaults */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "dlig" 1;
  }

  /* Improved currency symbol rendering */
  .currency {
    font-feature-settings: "kern" 1, "liga" 1, "tnum" 1, "lnum" 1;
    font-variant-numeric: tabular-nums;
  }

  /* Better button text */
  button {
    @apply font-accent;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Navigation text optimization */
  nav {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }
}

/* Component-specific typography */
@layer components {
  .text-luxury {
    @apply font-heading font-semibold tracking-tight text-primary-800;
    font-feature-settings: "kern" 1, "liga" 1, "dlig" 1;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(30, 41, 59, 0.1);
  }

  .text-premium {
    @apply font-accent font-medium text-secondary-600;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  .text-price {
    @apply font-accent font-bold currency text-secondary-700;
    background: linear-gradient(135deg, #9a7b1a 0%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-location {
    @apply font-body font-medium text-charcoal-700;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Luxury Card Styles */
  .card-luxury {
    @apply bg-gradient-to-br from-accent-50 to-accent-100 shadow-card-premium border border-accent-200/50;
    backdrop-filter: blur(10px);
  }

  .card-luxury:hover {
    @apply shadow-luxury-lg;
    transform: translateY(-2px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium Button Styles */
  .btn-luxury {
    @apply bg-gradient-to-r from-primary-800 to-primary-700 text-accent-50 shadow-btn;
    @apply hover:shadow-btn-hover hover:from-primary-700 hover:to-primary-600;
    @apply transition-all duration-300 ease-out;
    @apply border border-primary-600/20;
  }

  .btn-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-secondary-luxury {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-500 text-primary-900 shadow-btn-gold;
    @apply hover:shadow-btn-gold-hover hover:from-secondary-500 hover:to-secondary-400;
    @apply transition-all duration-300 ease-out;
    @apply border border-secondary-400/30;
  }

  .btn-secondary-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-outline-luxury {
    @apply border-2 border-primary-700 text-primary-700 bg-transparent;
    @apply hover:bg-primary-700 hover:text-accent-50;
    @apply transition-all duration-300 ease-out;
    @apply shadow-card;
  }

  .btn-outline-luxury:hover {
    @apply shadow-btn;
    transform: translateY(-1px);
  }

  /* Glass Effect Components */
  .glass-effect {
    @apply bg-glass backdrop-blur-sm border border-accent-200/30 shadow-glass;
  }

  .glass-dark {
    @apply bg-glass-dark backdrop-blur-sm border border-primary-700/30 shadow-glass-dark;
  }

  /* Section Backgrounds */
  .section-luxury {
    @apply bg-gradient-to-br from-accent-50 via-accent-100 to-accent-50;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(30, 41, 59, 0.03) 0%,
        transparent 50%
      );
  }

  .section-premium {
    @apply bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900;
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(212, 175, 55, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      );
  }

  /* Luxury Dividers */
  .divider-luxury {
    @apply h-px bg-gradient-to-r from-transparent via-secondary-400 to-transparent;
  }

  .divider-premium {
    @apply h-0.5 bg-gradient-to-r from-transparent via-secondary-500 to-transparent;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
  }
}
