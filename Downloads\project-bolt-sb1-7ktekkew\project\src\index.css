@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography Base Styles */
@layer base {
  html {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0,
      "onum" 1, "lnum" 0, "dlig" 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply font-body text-body text-charcoal;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  /* Heading defaults */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "dlig" 1;
  }

  /* Improved currency symbol rendering */
  .currency {
    font-feature-settings: "kern" 1, "liga" 1, "tnum" 1, "lnum" 1;
    font-variant-numeric: tabular-nums;
  }

  /* Better button text */
  button {
    @apply font-accent;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Navigation text optimization */
  nav {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }
}

/* Component-specific typography */
@layer components {
  .text-luxury {
    @apply font-heading font-semibold tracking-tight;
    font-feature-settings: "kern" 1, "liga" 1, "dlig" 1;
  }

  .text-premium {
    @apply font-accent font-medium;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  .text-price {
    @apply font-accent font-bold currency;
  }

  .text-location {
    @apply font-body font-medium;
    font-feature-settings: "kern" 1, "liga" 1;
  }
}
