/** @type {import('tailwindcss').Config} */
export default {
  content: [ './index.html', './src/**/*.{js,ts,jsx,tsx}' ],
  theme: {
    extend: {
      fontFamily: {
        'heading': [ 'Crimson Text', 'Georgia', 'serif' ],
        'body': [ 'Inter', 'system-ui', '-apple-system', 'sans-serif' ],
        'accent': [ 'Poppins', 'Inter', 'sans-serif' ],
        'sans': [ 'Inter', 'system-ui', '-apple-system', 'sans-serif' ],
        'serif': [ 'Crimson Text', 'Georgia', 'serif' ],
      },
      colors: {
        primary: '#1A2B3C',
        secondary: '#C5A572',
        accent: '#F5F5F5',
        charcoal: '#333333',
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
      },
      fontSize: {
        'hero': [ '3.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' } ],
        'hero-mobile': [ '2.25rem', { lineHeight: '1.15', letterSpacing: '-0.01em' } ],
        'section': [ '2.5rem', { lineHeight: '1.2', letterSpacing: '-0.01em' } ],
        'section-mobile': [ '1.875rem', { lineHeight: '1.25', letterSpacing: '-0.005em' } ],
        'heading-lg': [ '2rem', { lineHeight: '1.3', letterSpacing: '-0.005em' } ],
        'heading-md': [ '1.5rem', { lineHeight: '1.35', letterSpacing: '0em' } ],
        'heading-sm': [ '1.25rem', { lineHeight: '1.4', letterSpacing: '0em' } ],
        'body-lg': [ '1.125rem', { lineHeight: '1.7', letterSpacing: '0em' } ],
        'body': [ '1rem', { lineHeight: '1.65', letterSpacing: '0em' } ],
        'body-sm': [ '0.875rem', { lineHeight: '1.6', letterSpacing: '0.01em' } ],
        'caption': [ '0.75rem', { lineHeight: '1.5', letterSpacing: '0.02em' } ],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'scroll': 'scroll 40s linear infinite',
        'fade-in': 'fadeIn 0.8s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'scale-in': 'scaleIn 0.6s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        glow: {
          'from': { boxShadow: '0 0 20px #C5A572' },
          'to': { boxShadow: '0 0 30px #C5A572, 0 0 40px #C5A572' },
        },
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },
      backgroundImage: {
        'hero-gradient': 'linear-gradient(135deg, rgba(26, 43, 60, 0.9) 0%, rgba(26, 43, 60, 0.6) 100%)',
        'card-gradient': 'linear-gradient(135deg, rgba(245, 245, 245, 0.1) 0%, rgba(197, 165, 114, 0.1) 100%)',
        'glass': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
      },
      backdropBlur: {
        'xs': '2px',
      },
      boxShadow: {
        'luxury': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
      },
    },
  },
  plugins: [],
};